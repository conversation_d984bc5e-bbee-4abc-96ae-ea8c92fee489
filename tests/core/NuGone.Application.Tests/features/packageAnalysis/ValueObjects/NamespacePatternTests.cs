using NuGone.Domain.Features.PackageAnalysis.ValueObjects;
using Xunit;

namespace NuGone.Application.Tests.Features.PackageAnalysis.ValueObjects;

/// <summary>
/// Tests for NamespacePattern value object.
/// Validates RFC-0002 namespace pattern matching functionality with RFC-0004 safety considerations.
/// </summary>
public class NamespacePatternTests
{
    [Fact]
    public void Constructor_WithValidPattern_ShouldCreateInstance()
    {
        // Arrange & Act
        var pattern = new NamespacePattern("System.Text.Json");

        // Assert
        pattern.Pattern.ShouldBe("System.Text.Json");
        pattern.IsExact.ShouldBeTrue();
        pattern.IsWildcard.ShouldBeFalse();
    }

    [Fact]
    public void Constructor_WithWildcardPattern_ShouldDetectWildcard()
    {
        // Arrange & Act
        var pattern = new NamespacePattern("System.*");

        // Assert
        pattern.Pattern.ShouldBe("System.*");
        pattern.IsExact.ShouldBeFalse();
        pattern.IsWildcard.ShouldBeTrue();
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void Constructor_WithInvalidPattern_ShouldThrowException(string? invalidPattern)
    {
        // Act & Assert
        Should.Throw<ArgumentException>(() => new NamespacePattern(invalidPattern!));
    }

    [Fact]
    public void Constructor_WithWhitespacePattern_ShouldTrimWhitespace()
    {
        // Arrange & Act
        var pattern = new NamespacePattern("  System.Text.Json  ");

        // Assert
        pattern.Pattern.ShouldBe("System.Text.Json");
    }

    [Theory]
    [InlineData("System.Text.Json", "System.Text.Json", true)]
    [InlineData("System.Text.Json", "system.text.json", true)] // Case insensitive
    [InlineData("System.Text.Json", "System.Text", false)]
    [InlineData("System.Text.Json", "System.Text.Json.Serialization", false)]
    [InlineData("System.Text.Json", "", false)]
    [InlineData("System.Text.Json", null, false)]
    public void Matches_WithExactPattern_ShouldMatchCorrectly(
        string pattern,
        string? testNamespace,
        bool expectedMatch
    )
    {
        // Arrange
        var namespacePattern = new NamespacePattern(pattern);

        // Act
        var result = namespacePattern.Matches(testNamespace!);

        // Assert
        result.ShouldBe(expectedMatch);
    }

    [Theory]
    [InlineData("System.*", "System.Text", true)]
    [InlineData("System.*", "System.Text.Json", true)]
    [InlineData("System.*", "System", false)] // Exact match without trailing part
    [InlineData("System.*", "Microsoft.Extensions", false)]
    [InlineData("*.Json", "System.Text.Json", true)]
    [InlineData("*.Json", "Newtonsoft.Json", true)]
    [InlineData("*.Json", "System.Text", false)]
    public void Matches_WithWildcardPattern_ShouldMatchCorrectly(
        string pattern,
        string testNamespace,
        bool expectedMatch
    )
    {
        // Arrange
        var namespacePattern = new NamespacePattern(pattern);

        // Act
        var result = namespacePattern.Matches(testNamespace);

        // Assert
        result.ShouldBe(expectedMatch);
    }

    [Fact]
    public void Exact_ShouldCreateExactPattern()
    {
        // Act
        var pattern = NamespacePattern.Exact("System.Text.Json");

        // Assert
        pattern.Pattern.ShouldBe("System.Text.Json");
        pattern.IsExact.ShouldBeTrue();
        pattern.IsWildcard.ShouldBeFalse();
    }

    [Fact]
    public void Prefix_ShouldCreatePrefixPattern()
    {
        // Act
        var pattern = NamespacePattern.Prefix("System");

        // Assert
        pattern.Pattern.ShouldBe("System*");
        pattern.IsExact.ShouldBeFalse();
        pattern.IsWildcard.ShouldBeTrue();
    }

    [Fact]
    public void Suffix_ShouldCreateSuffixPattern()
    {
        // Act
        var pattern = NamespacePattern.Suffix("Json");

        // Assert
        pattern.Pattern.ShouldBe("*Json");
        pattern.IsExact.ShouldBeFalse();
        pattern.IsWildcard.ShouldBeTrue();
    }

    [Fact]
    public void Wildcard_ShouldCreateWildcardPattern()
    {
        // Act
        var pattern = NamespacePattern.Wildcard("System.*.Json");

        // Assert
        pattern.Pattern.ShouldBe("System.*.Json");
        pattern.IsExact.ShouldBeFalse();
        pattern.IsWildcard.ShouldBeTrue();
    }

    [Fact]
    public void Equals_WithSamePattern_ShouldReturnTrue()
    {
        // Arrange
        var pattern1 = new NamespacePattern("System.Text.Json");
        var pattern2 = new NamespacePattern("System.Text.Json");

        // Act & Assert
        pattern1.Equals(pattern2).ShouldBeTrue();
        (pattern1 == pattern2).ShouldBeTrue();
        (pattern1 != pattern2).ShouldBeFalse();
    }

    [Fact]
    public void Equals_WithDifferentPattern_ShouldReturnFalse()
    {
        // Arrange
        var pattern1 = new NamespacePattern("System.Text.Json");
        var pattern2 = new NamespacePattern("Newtonsoft.Json");

        // Act & Assert
        pattern1.Equals(pattern2).ShouldBeFalse();
        (pattern1 == pattern2).ShouldBeFalse();
        (pattern1 != pattern2).ShouldBeTrue();
    }

    [Fact]
    public void Equals_WithCaseInsensitivePattern_ShouldReturnTrue()
    {
        // Arrange
        var pattern1 = new NamespacePattern("System.Text.Json");
        var pattern2 = new NamespacePattern("system.text.json");

        // Act & Assert
        pattern1.Equals(pattern2).ShouldBeTrue();
    }

    [Fact]
    public void Equals_WithNull_ShouldReturnFalse()
    {
        // Arrange
        var pattern = new NamespacePattern("System.Text.Json");

        // Act & Assert
        pattern.Equals(null).ShouldBeFalse();
    }

    [Fact]
    public void GetHashCode_WithSamePattern_ShouldReturnSameHash()
    {
        // Arrange
        var pattern1 = new NamespacePattern("System.Text.Json");
        var pattern2 = new NamespacePattern("System.Text.Json");

        // Act & Assert
        pattern1.GetHashCode().ShouldBe(pattern2.GetHashCode());
    }

    [Fact]
    public void GetHashCode_WithCaseInsensitivePattern_ShouldReturnSameHash()
    {
        // Arrange
        var pattern1 = new NamespacePattern("System.Text.Json");
        var pattern2 = new NamespacePattern("system.text.json");

        // Act & Assert
        pattern1.GetHashCode().ShouldBe(pattern2.GetHashCode());
    }

    [Fact]
    public void ToString_ShouldReturnPattern()
    {
        // Arrange
        var pattern = new NamespacePattern("System.Text.Json");

        // Act & Assert
        pattern.ToString().ShouldBe("System.Text.Json");
    }

    [Fact]
    public void Matches_WithComplexWildcardScenarios_ShouldHandleCorrectly()
    {
        // Arrange - RFC-0004 Safety: Complex pattern matching scenarios
        var scenarios = new[]
        {
            (
                Pattern: "Microsoft.Extensions.*",
                Namespace: "Microsoft.Extensions.DependencyInjection",
                Expected: true
            ),
            (
                Pattern: "Microsoft.Extensions.*",
                Namespace: "Microsoft.Extensions.Logging.Abstractions",
                Expected: true
            ),
            (Pattern: "*.AspNetCore", Namespace: "Microsoft.AspNetCore", Expected: true),
            (Pattern: "*.AspNetCore", Namespace: "Swashbuckle.AspNetCore", Expected: true),
            (Pattern: "System.Text.*", Namespace: "System.Text", Expected: false), // No trailing part
            (Pattern: "System.Text.*", Namespace: "System.Threading", Expected: false), // Different namespace
        };

        foreach (var (patternStr, namespaceStr, expected) in scenarios)
        {
            // Act
            var pattern = new NamespacePattern(patternStr);
            var result = pattern.Matches(namespaceStr);

            // Assert
            result.ShouldBe(
                expected,
                $"Pattern '{patternStr}' matching '{namespaceStr}' should be {expected}"
            );
        }
    }
}
