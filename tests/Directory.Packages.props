<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <!-- Common Packages Used Across Multiple Projects -->
  <ItemGroup Label="Microsoft Extensions - Common">
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="9.0.7" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="9.0.7" />
  </ItemGroup>
  <!-- Common Test Framework Packages for All Test Projects -->
  <ItemGroup Label="Testing - Common">
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
    <PackageVersion Include="xunit" Version="2.9.3" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="3.1.2" />
    <PackageVersion Include="Shouldly" Version="4.3.0" />
    <PackageVersion Include="Moq" Version="4.20.72" />
    <PackageVersion Include="coverlet.collector" Version="6.0.2" />
    <PackageVersion Include="Spectre.Console" Version="0.50.0" />
    <PackageVersion Include="Spectre.Console.Testing" Version="0.50.0" />
    <PackageVersion Include="System.IO.Abstractions.TestingHelpers" Version="21.1.3" />
  </ItemGroup>
</Project>
